{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/app/page.tsx"], "sourcesContent": ["import React from 'react'\nimport Header from '@/components/layout/header'\n\nexport default function HomePage() {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header />\n      <main className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <section className=\"py-20 text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent mb-6\">\n            Modern Website with Skiper UI\n          </h1>\n          <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto mb-8\">\n            Experience the power of modern web design with advanced animations,\n            beautiful components, and cutting-edge visual effects.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button className=\"px-8 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors\">\n              Get Started\n            </button>\n            <button className=\"px-8 py-3 border border-border rounded-lg font-medium hover:bg-accent transition-colors\">\n              Learn More\n            </button>\n          </div>\n        </section>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAsH;;;;;;sCAGpI,8OAAC;4BAAE,WAAU;sCAAuD;;;;;;sCAIpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAA4G;;;;;;8CAG9H,8OAAC;oCAAO,WAAU;8CAA0F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxH", "debugId": null}}]}