{"name": "react-remove-scroll-bar", "version": "2.3.8", "description": "Removes body scroll without content _shake_", "main": "dist/es5/index.js", "jsnext:main": "dist/es2015/index.js", "module": "dist/es2015/index.js", "types": "dist/es5/index.d.ts", "scripts": {"dev": "lib-builder dev", "test": "jest", "test:ci": "jest --runInBand --coverage", "build": "lib-builder build && yarn size:report", "release": "yarn build && yarn test", "size": "yarn size-limit", "size:report": "yarn --silent size-limit --json > .size.json", "lint": "lib-builder lint", "format": "lib-builder format", "update": "lib-builder update", "prepublish": "yarn build && yarn changelog", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "changelog:rewrite": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "storybook": "start-storybook -p 6006"}, "keywords": ["scroll"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@size-limit/preset-small-lib": "^11.0.2", "size-limit": "^11.0.2", "@storybook/react": "^6.4.22", "@testing-library/react": "^12.1.5", "@types/react": "^16.14.56", "@theuiteam/lib-builder": "^0.1.4", "react": "^16.8.6", "react-dom": "^16.8.6"}, "engines": {"node": ">=10"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "files": ["dist", "constants"], "repository": "https://github.com/theKashey/react-remove-scroll-bar", "dependencies": {"react-style-singleton": "^2.2.2", "tslib": "^2.0.0"}, "module:es2019": "dist/es2019/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx}": ["prettier --write", "eslint --fix", "git add"], "*.{js,css,json,md}": ["prettier --write", "git add"]}, "prettier": {"printWidth": 120, "trailingComma": "es5", "tabWidth": 2, "semi": true, "singleQuote": true}}